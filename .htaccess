# Enable rewrite engine
RewriteEngine On

# Set the base directory
RewriteBase /Modparts/

# Handle API requests
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Set proper MIME types for JavaScript files
AddType application/javascript .js
AddType application/javascript .mjs

# Set proper MIME type for CSV files
AddType text/csv .csv

# Explicitly handle the main JavaScript file
RewriteRule ^assets/index.js$ index.php [QSA,L]

# Handle other asset requests
RewriteRule ^assets/(.*)$ - [L]
RewriteRule ^vite.svg$ - [L]

# Redirect all other requests to index.php for client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Add CORS headers
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Enable error logging
php_flag display_errors on
php_value error_reporting 2047

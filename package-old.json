{"name": "yamaha-rd-parts-vercel", "version": "1.0.0", "description": "Yamaha RD Parts Shop - E-commerce application for Vercel deployment", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:frontend\"", "dev:server": "nodemon dev-server.js", "dev:frontend": "cd frontend && npm run dev -- --port 5173", "build": "cd frontend && npm run build && npm run build:copy", "build:copy": "cd frontend && cp -r dist/* ../public/", "start": "node dev-server.js", "install-frontend": "cd frontend && npm install", "vercel-build": "cd frontend && npm install && npm run build"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.18.2", "form-data": "^4.0.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.2"}, "keywords": ["yamaha", "motorcycle", "parts", "ecommerce", "vercel", "supabase"], "author": "Your Name", "license": "MIT"}
<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2023-11-15T12:00:00.000Z" agent="Mozilla/5.0" etag="database-schema" version="21.7.5" type="device">
  <diagram id="yamaha_rd_parts_schema" name="Yamaha RD Parts Database Schema">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- Categories Table -->
        <mxCell id="categories" value="categories" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="240" height="150" as="geometry" />
        </mxCell>
        <mxCell id="categories-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="categories" vertex="1">
          <mxGeometry y="30" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="categories-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="categories-pk" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-name" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="categories" vertex="1">
          <mxGeometry y="60" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-name-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="categories-name" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-name-value" value="name VARCHAR(255) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="categories-name" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-description" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="categories" vertex="1">
          <mxGeometry y="90" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-description-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="categories-description" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-description-value" value="description TEXT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="categories-description" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="categories" vertex="1">
          <mxGeometry y="120" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="categories-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="categories-timestamps-value" value="created_at, updated_at TIMESTAMP" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="categories-timestamps" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>

        <!-- Products Table -->
        <mxCell id="products" value="products" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="400" y="40" width="280" height="270" as="geometry" />
        </mxCell>
        <mxCell id="products-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="30" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="products-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="products-pk" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-fk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="60" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-fk-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-fk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-fk-value" value="category_id INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-fk" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-name" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="90" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-name-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-name" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-name-value" value="name VARCHAR(255) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-name" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-description" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="120" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-description-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-description" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-description-value" value="description TEXT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-description" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-condition" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="150" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-condition-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-condition" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-condition-value" value="condition_status ENUM NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-condition" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-price" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="180" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-price-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-price" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-price-value" value="price DECIMAL(10, 2) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-price" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-quantity" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="210" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-quantity-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-quantity" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-quantity-value" value="quantity INT NOT NULL DEFAULT 0" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-quantity" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="products" vertex="1">
          <mxGeometry y="240" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="products-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="products-timestamps-value" value="image_url, created_at, updated_at" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="products-timestamps" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- Users Table -->
        <mxCell id="users" value="users" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="40" y="240" width="240" height="270" as="geometry" />
        </mxCell>
        <mxCell id="users-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="30" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="users-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="users-pk" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-email" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="60" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-email-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-email" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-email-value" value="email VARCHAR(255) NOT NULL UNIQUE" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-email" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-password" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="90" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-password-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-password" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-password-value" value="password VARCHAR(255) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-password" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-name" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="120" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-name-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-name" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-name-value" value="first_name, last_name VARCHAR(100)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-name" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-address" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="150" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-address-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-address" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-address-value" value="address TEXT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-address" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-phone" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="180" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-phone-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-phone" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-phone-value" value="phone VARCHAR(20)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-phone" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-role" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="210" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-role-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-role" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-role-value" value="role ENUM DEFAULT 'customer'" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-role" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="users" vertex="1">
          <mxGeometry y="240" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="users-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="users-timestamps-value" value="created_at, updated_at TIMESTAMP" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="users-timestamps" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>

        <!-- Orders Table -->
        <mxCell id="orders" value="orders" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="400" y="350" width="280" height="240" as="geometry" />
        </mxCell>
        <mxCell id="orders-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="30" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="orders-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="orders-pk" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-fk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="60" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-fk-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-fk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-fk-value" value="user_id INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-fk" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-total" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="90" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-total-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-total" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-total-value" value="total_amount DECIMAL(10, 2) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-total" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-status" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="120" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-status-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-status" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-status-value" value="status ENUM NOT NULL DEFAULT 'pending'" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-status" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-address" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="150" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-address-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-address" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-address-value" value="shipping_address TEXT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-address" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-payment" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="180" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-payment-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-payment" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-payment-value" value="payment_method VARCHAR(50) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-payment" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="orders" vertex="1">
          <mxGeometry y="210" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="orders-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-timestamps-value" value="created_at, updated_at TIMESTAMP" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="orders-timestamps" vertex="1">
          <mxGeometry x="30" width="250" height="30" as="geometry" />
        </mxCell>

        <!-- Order Items Table -->
        <mxCell id="order_items" value="order_items" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="720" y="350" width="240" height="210" as="geometry" />
        </mxCell>
        <mxCell id="order_items-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="30" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="order_items-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="order_items-pk" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk1" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="60" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk1-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="order_items-fk1" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk1-value" value="order_id INT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="order_items-fk1" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk2" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="90" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk2-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="order_items-fk2" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-fk2-value" value="product_id INT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="order_items-fk2" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-quantity" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="120" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-quantity-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="order_items-quantity" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-quantity-value" value="quantity INT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="order_items-quantity" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-price" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="150" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-price-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="order_items-price" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-price-value" value="price DECIMAL(10, 2) NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="order_items-price" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="order_items" vertex="1">
          <mxGeometry y="180" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="order_items-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_items-timestamps-value" value="created_at TIMESTAMP" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="order_items-timestamps" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>

        <!-- Cart Items Table -->
        <mxCell id="cart_items" value="cart_items" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="720" y="40" width="240" height="210" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-pk" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=1;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="cart_items" vertex="1">
          <mxGeometry y="30" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" parent="cart_items-pk" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-pk-value" value="id INT AUTO_INCREMENT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;" parent="cart_items-pk" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk1" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="cart_items" vertex="1">
          <mxGeometry y="60" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk1-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="cart_items-fk1" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk1-value" value="user_id INT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="cart_items-fk1" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk2" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="cart_items" vertex="1">
          <mxGeometry y="90" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk2-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="cart_items-fk2" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-fk2-value" value="product_id INT NOT NULL" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="cart_items-fk2" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-quantity" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="cart_items" vertex="1">
          <mxGeometry y="120" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-quantity-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="cart_items-quantity" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-quantity-value" value="quantity INT NOT NULL DEFAULT 1" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="cart_items-quantity" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-timestamps" value="" style="shape=partialRectangle;collapsible=0;dropTarget=0;pointerEvents=0;fillColor=none;top=0;left=0;bottom=0;right=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="cart_items" vertex="1">
          <mxGeometry y="150" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-timestamps-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" parent="cart_items-timestamps" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_items-timestamps-value" value="created_at, updated_at TIMESTAMP" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" parent="cart_items-timestamps" vertex="1">
          <mxGeometry x="30" width="210" height="30" as="geometry" />
        </mxCell>

        <!-- Relationships -->
        <!-- Products to Categories -->
        <mxCell id="rel-products-categories" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="products-fk" target="categories-pk">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Orders to Users -->
        <mxCell id="rel-orders-users" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="orders-fk" target="users-pk">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="340" y="375" />
              <mxPoint x="340" y="285" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Order Items to Orders -->
        <mxCell id="rel-order_items-orders" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="order_items-fk1" target="orders-pk">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Order Items to Products -->
        <mxCell id="rel-order_items-products" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="order_items-fk2" target="products-pk">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="700" y="425" />
              <mxPoint x="700" y="85" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Cart Items to Users -->
        <mxCell id="rel-cart_items-users" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="cart_items-fk1" target="users">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="160" y="115" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Cart Items to Products -->
        <mxCell id="rel-cart_items-products" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;startFill=0;endArrow=ERone;endFill=0;" edge="1" parent="1" source="cart_items-fk2" target="products-pk">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

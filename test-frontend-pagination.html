<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Pagination Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover {
            background: #f0f0f0;
        }
        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .info {
            text-align: center;
            color: #666;
            margin: 10px 0;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Frontend Pagination Test</h1>
    <p>This page tests the pagination API directly from the frontend perspective.</p>
    
    <div class="debug" id="debugInfo">
        <strong>Debug Info:</strong><br>
        Loading...
    </div>
    
    <div class="info" id="info">Loading...</div>
    
    <div class="product-grid" id="productGrid">
        <!-- Products will be loaded here -->
    </div>
    
    <div class="pagination" id="pagination">
        <!-- Pagination will be loaded here -->
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        let totalItems = 0;
        
        async function loadProducts() {
            const debugDiv = document.getElementById('debugInfo');
            
            try {
                debugDiv.innerHTML = '<strong>Debug Info:</strong><br>Making API request...';
                
                // Test the exact same API call the frontend makes
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: 12,
                    sortBy: 'created_at',
                    sortOrder: 'desc'
                });
                
                const url = `http://localhost:3000/api/products?${params}`;
                debugDiv.innerHTML += `<br>URL: ${url}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                debugDiv.innerHTML += `<br>Response Status: ${response.status}`;
                debugDiv.innerHTML += `<br>Response OK: ${response.ok}`;
                debugDiv.innerHTML += `<br>Products Count: ${data.data?.length || 0}`;
                debugDiv.innerHTML += `<br>Pagination: ${JSON.stringify(data.pagination, null, 2)}`;
                
                if (data.data) {
                    displayProducts(data.data);
                    updatePagination(data.pagination);
                    updateInfo(data.pagination);
                } else {
                    document.getElementById('productGrid').innerHTML = '<p>No products found</p>';
                    debugDiv.innerHTML += '<br><strong>ERROR: No data field in response</strong>';
                }
                
                // Log full response for debugging
                console.log('Full API Response:', data);
                
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('productGrid').innerHTML = '<p>Error loading products</p>';
                debugDiv.innerHTML += `<br><strong>ERROR: ${error.message}</strong>`;
            }
        }
        
        function displayProducts(products) {
            const grid = document.getElementById('productGrid');
            grid.innerHTML = products.map(product => `
                <div class="product-card">
                    <h3>${product.name}</h3>
                    <p><strong>$${parseFloat(product.price).toFixed(2)}</strong></p>
                    <p>Condition: ${product.condition_status}</p>
                    <p>Stock: ${product.quantity}</p>
                    <p>Category: ${product.categories?.name || 'N/A'}</p>
                </div>
            `).join('');
        }
        
        function updatePagination(pagination) {
            if (!pagination) {
                document.getElementById('pagination').innerHTML = '<p>No pagination data</p>';
                return;
            }
            
            totalPages = pagination.totalPages;
            totalItems = pagination.totalItems;
            currentPage = pagination.currentPage;
            
            const paginationDiv = document.getElementById('pagination');
            let html = '';
            
            // Previous button
            html += `<button onclick="goToPage(${currentPage - 1})" ${!pagination.hasPrevPage ? 'disabled' : ''}>Previous</button>`;
            
            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            if (startPage > 1) {
                html += `<button onclick="goToPage(1)">1</button>`;
                if (startPage > 2) html += '<span>...</span>';
            }
            
            for (let i = startPage; i <= endPage; i++) {
                html += `<button onclick="goToPage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) html += '<span>...</span>';
                html += `<button onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }
            
            // Next button
            html += `<button onclick="goToPage(${currentPage + 1})" ${!pagination.hasNextPage ? 'disabled' : ''}>Next</button>`;
            
            paginationDiv.innerHTML = html;
        }
        
        function updateInfo(pagination) {
            if (!pagination) {
                document.getElementById('info').textContent = 'No pagination info available';
                return;
            }
            
            const start = ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1;
            const end = Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems);
            
            document.getElementById('info').textContent = 
                `Showing ${start} to ${end} of ${pagination.totalItems} products (Page ${pagination.currentPage} of ${pagination.totalPages})`;
        }
        
        function goToPage(page) {
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                loadProducts();
            }
        }
        
        // Load initial products
        loadProducts();
    </script>
</body>
</html>
